-- ========================================
-- FIX EMPLOYEE-SPECIFIC CONFLICT DETECTION
-- ========================================
-- This migration fixes the unique constraint to allow employee-specific
-- time slot booking instead of global time slot conflicts.
--
-- Problem: The current unique constraint prevents any two appointments
-- from having the same date and time, regardless of employee assignment.
-- This prevents multiple employees from having appointments at the same time.
--
-- Solution: Update the constraint to include employee_id so that conflicts
-- only occur when the same employee is double-booked.

-- Step 1: Check current constraints
SELECT indexname, indexdef
FROM pg_indexes
WHERE tablename = 'appointments'
AND indexname LIKE '%unique%';

-- Step 2: Drop the existing global unique constraint
DROP INDEX IF EXISTS unique_predefined_appointment_slot;

-- Step 3: Create a new employee-specific unique constraint for predefined slots
-- This allows multiple employees to have appointments at the same time,
-- but prevents the same employee from being double-booked
CREATE UNIQUE INDEX unique_employee_predefined_appointment_slot
ON appointments (employee_id, data_appuntamento, orario)
WHERE custom_time_range = false AND status != 'cancelled' AND employee_id IS NOT NULL;

-- Step 4: For appointments without a specific employee (employee_id IS NULL),
-- we still want to prevent global conflicts since these represent
-- "any available employee" bookings
CREATE UNIQUE INDEX unique_global_predefined_appointment_slot
ON appointments (data_appuntamento, orario)
WHERE custom_time_range = false AND status != 'cancelled' AND employee_id IS NULL;

-- Step 5: Verify the new constraints were created
SELECT indexname, indexdef
FROM pg_indexes
WHERE tablename = 'appointments'
AND indexname LIKE '%unique%';

-- Add comments explaining the new constraint system
COMMENT ON INDEX unique_employee_predefined_appointment_slot IS 
'Prevents double-booking of specific employees for predefined time slots';

COMMENT ON INDEX unique_global_predefined_appointment_slot IS 
'Prevents global conflicts for appointments without specific employee assignment';

-- Verify the changes
DO $$
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE 'EMPLOYEE-SPECIFIC CONFLICT DETECTION FIX COMPLETED!';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Changes applied:';
    RAISE NOTICE '- Dropped global unique constraint on (date, time)';
    RAISE NOTICE '- Added employee-specific constraint on (employee_id, date, time)';
    RAISE NOTICE '- Added fallback global constraint for unassigned appointments';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Result: Multiple employees can now have appointments at the same time';
    RAISE NOTICE 'But individual employees cannot be double-booked';
    RAISE NOTICE '========================================';
END $$;
