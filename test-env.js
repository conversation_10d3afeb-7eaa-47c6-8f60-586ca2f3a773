// Carica manualmente le variabili d'ambiente
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carica il file .env.local
dotenv.config({ path: join(__dirname, '.env.local') });

// Test per verificare le variabili d'ambiente
console.log('=== TEST VARIABILI D\'AMBIENTE ===');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'SET' : 'NOT SET');
console.log('SUPABASE_API:', process.env.SUPABASE_API ? 'SET' : 'NOT SET');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET' : 'NOT SET');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'SET' : 'NOT SET');
console.log('ADMIN_USERNAME:', process.env.ADMIN_USERNAME ? 'SET' : 'NOT SET');
console.log('ADMIN_PASSWORD:', process.env.ADMIN_PASSWORD ? 'SET' : 'NOT SET');

// Test credenziali admin
console.log('\n=== TEST CREDENZIALI ADMIN ===');
console.log('Username:', process.env.ADMIN_USERNAME);
console.log('Password:', process.env.ADMIN_PASSWORD ? '[HIDDEN]' : 'NOT SET');

// Test connessione Supabase
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_API;

if (supabaseUrl && supabaseAnonKey) {
  console.log('\n=== TEST CONNESSIONE SUPABASE ===');
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  try {
    // Test semplice query
    const { data, error } = await supabase
      .from('admin_users')
      .select('username')
      .limit(1);
    
    if (error) {
      console.log('Errore connessione Supabase:', error.message);
    } else {
      console.log('Connessione Supabase: OK');
      console.log('Utenti admin trovati:', data ? data.length : 0);
    }
  } catch (err) {
    console.log('Errore test Supabase:', err.message);
  }
} else {
  console.log('\n=== ERRORE ===');
  console.log('URL o API key Supabase mancanti!');
}
