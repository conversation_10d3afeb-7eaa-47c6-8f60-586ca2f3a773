@import "tailwindcss";

:root {
  /* CAF Color Palette */
  --primary-red: #B42C2C;
  --secondary-blue: #252B59;
  --light-background: #F7F7F5;
  --border-gray: #D1D1D1;
  --primary-text: #1F1F1F;
  --secondary-text: #555555;
  --hover-accent: #6B1F1F;
  --white: #ffffff;

  --background: var(--light-background);
  --foreground: var(--primary-text);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary-red: var(--primary-red);
  --color-secondary-blue: var(--secondary-blue);
  --color-light-background: var(--light-background);
  --color-border-gray: var(--border-gray);
  --color-primary-text: var(--primary-text);
  --color-secondary-text: var(--secondary-text);
  --color-hover-accent: var(--hover-accent);
  --color-white: var(--white);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

/* Custom form styles */
.form-input {
  @apply w-full px-4 py-3 border border-[var(--border-gray)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent transition-all duration-200;
}

.form-label {
  @apply block text-sm font-medium text-[var(--primary-text)] mb-2;
}

.btn-primary {
  @apply w-full bg-[var(--primary-red)] text-white py-3 px-6 rounded-lg font-semibold text-lg hover:bg-[var(--hover-accent)] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:ring-offset-2;
}

.error-message {
  @apply text-[var(--primary-red)] text-sm mt-1;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .form-input {
    @apply text-base; /* Prevents zoom on iOS */
  }

  .btn-primary {
    @apply py-4 text-base; /* Larger touch target */
  }
}

/* Focus styles for better accessibility */
.form-input:focus {
  @apply ring-2 ring-[var(--primary-red)] ring-offset-2;
}

/* Custom select styling */
select.form-input {
  @apply appearance-none bg-white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}
