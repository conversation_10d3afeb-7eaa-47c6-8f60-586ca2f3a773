#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to test employee-specific conflict detection
 * 
 * This script tests the appointment creation logic to ensure:
 * 1. Multiple employees can have appointments at the same time
 * 2. Same employee cannot be double-booked
 * 3. Conflict detection functions work correctly
 * 
 * Run with: node scripts/test-conflict-detection.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '..', '.env.local') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Import the conflict detection functions
async function isTimeSlotAvailable(date, time, employeeId = null) {
  try {
    let query = supabase
      .from('appointments')
      .select('id, employee_id, orario, start_time, end_time, custom_time_range')
      .eq('data_appuntamento', date)
      .neq('status', 'cancelled');

    if (employeeId) {
      query = query.eq('employee_id', employeeId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error checking time slot availability:', error);
      throw new Error(`Failed to check time slot availability: ${error.message}`);
    }

    // Check for conflicts with existing appointments
    for (const appointment of data) {
      if (hasTimeConflict(time, time, appointment)) {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error checking time slot availability:', error);
    return false;
  }
}

function hasTimeConflict(requestStartTime, requestEndTime, existingAppointment) {
  try {
    // For predefined slots, assume 30-minute duration
    const requestStart = new Date(`2000-01-01T${requestStartTime}:00`);
    const requestEnd = requestEndTime === requestStartTime
      ? new Date(requestStart.getTime() + 30 * 60000) // Add 30 minutes for predefined slots
      : new Date(`2000-01-01T${requestEndTime}:00`);

    let existingStart, existingEnd;

    if (existingAppointment.custom_time_range && existingAppointment.start_time && existingAppointment.end_time) {
      // Custom time range appointment
      existingStart = new Date(`2000-01-01T${existingAppointment.start_time}:00`);
      existingEnd = new Date(`2000-01-01T${existingAppointment.end_time}:00`);
    } else if (existingAppointment.orario) {
      // Predefined slot appointment (30-minute duration)
      // Handle both HH:MM and HH:MM:SS formats
      const timeStr = existingAppointment.orario.length === 8
        ? existingAppointment.orario.substring(0, 5) // Remove seconds if present
        : existingAppointment.orario;
      existingStart = new Date(`2000-01-01T${timeStr}:00`);
      existingEnd = new Date(existingStart.getTime() + 30 * 60000);

      // Debug logging
      console.log(`      🔍 Debug hasTimeConflict:`);
      console.log(`         Request: ${requestStartTime} -> ${requestStart.toTimeString()}`);
      console.log(`         Existing: ${existingAppointment.orario} -> ${timeStr} -> ${existingStart.toTimeString()}`);
      console.log(`         Request range: ${requestStart.toTimeString()} - ${requestEnd.toTimeString()}`);
      console.log(`         Existing range: ${existingStart.toTimeString()} - ${existingEnd.toTimeString()}`);
    } else {
      return false; // Invalid appointment data
    }

    // Check for overlap: appointments overlap if requestStart < existingEnd AND requestEnd > existingStart
    const hasConflict = requestStart < existingEnd && requestEnd > existingStart;
    console.log(`         Overlap check: ${requestStart.getTime()} < ${existingEnd.getTime()} && ${requestEnd.getTime()} > ${existingStart.getTime()} = ${hasConflict}`);

    return hasConflict;
  } catch (error) {
    console.error('Error checking time conflict:', error);
    return false;
  }
}

async function testConflictDetection() {
  console.log('🧪 Testing Employee-Specific Conflict Detection');
  console.log('===============================================\n');

  try {
    // Get test employees
    const { data: employees, error: empError } = await supabase
      .from('employees')
      .select('id, name')
      .eq('is_active', true)
      .neq('name', 'Qualsiasi')
      .limit(3);

    if (empError || !employees || employees.length < 2) {
      throw new Error('Need at least 2 employees for testing');
    }

    const employee1 = employees[0];
    const employee2 = employees[1];
    const testDate = '2025-01-20'; // Future date
    const testTime = '14:00';

    console.log(`👥 Test employees:`);
    console.log(`   Employee 1: ${employee1.name} (${employee1.id})`);
    console.log(`   Employee 2: ${employee2.name} (${employee2.id})`);
    console.log(`📅 Test date: ${testDate}`);
    console.log(`⏰ Test time: ${testTime}\n`);

    // Clean up any existing test appointments
    console.log('🧹 Cleaning up existing test appointments...');
    await supabase
      .from('appointments')
      .delete()
      .eq('data_appuntamento', testDate)
      .in('employee_id', [employee1.id, employee2.id]);

    // Test 1: Check initial availability
    console.log('📋 Test 1: Initial availability check...');
    const available1 = await isTimeSlotAvailable(testDate, testTime, employee1.id);
    const available2 = await isTimeSlotAvailable(testDate, testTime, employee2.id);
    
    console.log(`   ${employee1.name} available: ${available1 ? '✅' : '❌'}`);
    console.log(`   ${employee2.name} available: ${available2 ? '✅' : '❌'}`);

    if (!available1 || !available2) {
      throw new Error('Initial availability check failed');
    }

    // Test 2: Create appointment for employee 1
    console.log('\n📋 Test 2: Creating appointment for employee 1...');
    const { data: apt1, error: createError1 } = await supabase
      .from('appointments')
      .insert({
        nome: 'Test',
        cognome: 'User1',
        telefono: '1234567890',
        email: '<EMAIL>',
        servizio: 'Servizi CAF',
        prestazione: 'Test Service',
        operatore: employee1.name,
        employee_id: employee1.id,
        data_appuntamento: testDate,
        orario: testTime,
        custom_time_range: false,
        status: 'confirmed'
      })
      .select()
      .single();

    if (createError1) {
      throw new Error(`Failed to create appointment for employee 1: ${createError1.message}`);
    }

    console.log(`   ✅ Appointment created for ${employee1.name}`);

    // Test 3: Check availability after first appointment
    console.log('\n📋 Test 3: Availability after first appointment...');

    // Debug: Check what appointments exist
    const { data: debugAppts, error: debugError } = await supabase
      .from('appointments')
      .select('id, employee_id, orario, data_appuntamento, status, custom_time_range')
      .eq('data_appuntamento', testDate);

    if (!debugError) {
      console.log(`   📊 Found ${debugAppts.length} appointments for ${testDate}:`);
      debugAppts.forEach(apt => {
        console.log(`      - ID: ${apt.id}, Employee: ${apt.employee_id}, Time: ${apt.orario}, Status: ${apt.status}`);
      });
    }

    const available1After = await isTimeSlotAvailable(testDate, testTime, employee1.id);
    const available2After = await isTimeSlotAvailable(testDate, testTime, employee2.id);

    console.log(`   ${employee1.name} available: ${available1After ? '✅' : '❌'} (should be ❌)`);
    console.log(`   ${employee2.name} available: ${available2After ? '✅' : '❌'} (should be ✅)`);

    if (available1After) {
      console.log('   🔍 Debugging why employee 1 is still available...');

      // Check the specific query for employee 1
      const { data: emp1Appts, error: emp1Error } = await supabase
        .from('appointments')
        .select('id, employee_id, orario, start_time, end_time, custom_time_range, status')
        .eq('data_appuntamento', testDate)
        .eq('employee_id', employee1.id)
        .neq('status', 'cancelled');

      if (!emp1Error) {
        console.log(`   📊 Employee 1 appointments: ${emp1Appts.length}`);
        emp1Appts.forEach(apt => {
          console.log(`      - Time: ${apt.orario}, Custom: ${apt.custom_time_range}, Status: ${apt.status}`);
          const conflict = hasTimeConflict(testTime, testTime, apt);
          console.log(`      - Conflict with ${testTime}: ${conflict}`);
        });
      }

      throw new Error('Employee 1 should not be available after booking');
    }
    if (!available2After) {
      throw new Error('Employee 2 should still be available');
    }

    // Test 4: Try to create another appointment for employee 1 (should fail)
    console.log('\n📋 Test 4: Attempting double-booking for employee 1...');
    const { error: doubleBookError } = await supabase
      .from('appointments')
      .insert({
        nome: 'Test',
        cognome: 'User1Duplicate',
        telefono: '1234567890',
        email: '<EMAIL>',
        servizio: 'Servizi CAF',
        prestazione: 'Test Service',
        operatore: employee1.name,
        employee_id: employee1.id,
        data_appuntamento: testDate,
        orario: testTime,
        custom_time_range: false,
        status: 'confirmed'
      });

    if (doubleBookError) {
      console.log(`   ✅ Double-booking prevented: ${doubleBookError.message}`);
    } else {
      throw new Error('Double-booking should have been prevented!');
    }

    // Test 5: Create appointment for employee 2 at same time (should succeed)
    console.log('\n📋 Test 5: Creating appointment for employee 2 at same time...');
    const { data: apt2, error: createError2 } = await supabase
      .from('appointments')
      .insert({
        nome: 'Test',
        cognome: 'User2',
        telefono: '1234567890',
        email: '<EMAIL>',
        servizio: 'Servizi CAF',
        prestazione: 'Test Service',
        operatore: employee2.name,
        employee_id: employee2.id,
        data_appuntamento: testDate,
        orario: testTime,
        custom_time_range: false,
        status: 'confirmed'
      })
      .select()
      .single();

    if (createError2) {
      throw new Error(`Failed to create appointment for employee 2: ${createError2.message}`);
    }

    console.log(`   ✅ Appointment created for ${employee2.name} at same time`);

    // Test 6: Final availability check
    console.log('\n📋 Test 6: Final availability check...');
    const finalAvailable1 = await isTimeSlotAvailable(testDate, testTime, employee1.id);
    const finalAvailable2 = await isTimeSlotAvailable(testDate, testTime, employee2.id);
    
    console.log(`   ${employee1.name} available: ${finalAvailable1 ? '✅' : '❌'} (should be ❌)`);
    console.log(`   ${employee2.name} available: ${finalAvailable2 ? '✅' : '❌'} (should be ❌)`);

    if (finalAvailable1 || finalAvailable2) {
      throw new Error('Both employees should be unavailable now');
    }

    // Clean up test appointments
    console.log('\n🧹 Cleaning up test appointments...');
    await supabase
      .from('appointments')
      .delete()
      .in('id', [apt1.id, apt2.id]);

    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Test Results Summary:');
    console.log('   ✅ Multiple employees can book the same time slot');
    console.log('   ✅ Same employee cannot be double-booked');
    console.log('   ✅ Conflict detection functions work correctly');
    console.log('   ✅ Database constraints are working properly');

    return true;

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    return false;
  }
}

// Run the tests
testConflictDetection().catch(console.error);
