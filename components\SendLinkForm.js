'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { isValidEmail } from '../lib/utils';

export default function SendLinkForm({ token }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm();

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      const response = await fetch('/api/send-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSubmitMessage('Link di invito inviato con successo!');
        reset();
      } else {
        setSubmitMessage(result.message || 'Errore nell\'invio del link. Riprova più tardi.');
      }
    } catch (error) {
      console.error('Error sending link:', error);
      setSubmitMessage('Errore di connessione. Riprova più tardi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold text-[var(--primary-text)] mb-6">
          Invia Link di Prenotazione
        </h2>
        
        <p className="text-[var(--secondary-text)] mb-6">
          Invia un link di invito per la prenotazione di un appuntamento direttamente via email al cliente.
        </p>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Nome */}
          <div>
            <label htmlFor="nome" className="form-label">
              Nome *
            </label>
            <input
              type="text"
              id="nome"
              className="form-input"
              {...register('nome', { 
                required: 'Il nome è obbligatorio',
                minLength: { value: 2, message: 'Il nome deve avere almeno 2 caratteri' }
              })}
            />
            {errors.nome && (
              <p className="error-message">{errors.nome.message}</p>
            )}
          </div>

          {/* Cognome */}
          <div>
            <label htmlFor="cognome" className="form-label">
              Cognome *
            </label>
            <input
              type="text"
              id="cognome"
              className="form-input"
              {...register('cognome', { 
                required: 'Il cognome è obbligatorio',
                minLength: { value: 2, message: 'Il cognome deve avere almeno 2 caratteri' }
              })}
            />
            {errors.cognome && (
              <p className="error-message">{errors.cognome.message}</p>
            )}
          </div>

          {/* Email */}
          <div>
            <label htmlFor="email" className="form-label">
              Email *
            </label>
            <input
              type="email"
              id="email"
              className="form-input"
              {...register('email', {
                required: 'L\'email è obbligatoria',
                validate: {
                  isValid: (value) =>
                    isValidEmail(value) || 'Inserisci un indirizzo email valido'
                }
              })}
            />
            {errors.email && (
              <p className="error-message">{errors.email.message}</p>
            )}
          </div>

          {/* Info Note */}
          <div className="text-sm text-[var(--secondary-text)] italic bg-blue-50 p-4 rounded-lg border border-blue-200">
            <strong>Nota:</strong> Il cliente riceverà una email con un link diretto per prenotare un appuntamento. 
            Questi dati non verranno salvati nel database.
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Invio in corso...' : 'Invia Link'}
          </button>

          {/* Submit Message */}
          {submitMessage && (
            <div className={`p-4 rounded-lg text-sm ${
              submitMessage.includes('successo')
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {submitMessage}
            </div>
          )}
        </form>
      </div>
    </div>
  );
}
