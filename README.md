# CAF Appointment Booking System

A comprehensive web application for Centro di Assistenza Fiscale (CAF) that provides online appointment booking with advanced employee management, availability scheduling, and administrative features. Built with Next.js, Supabase, and Tailwind CSS.

## 🌟 Features

### **Client Features**
- **Mobile-first responsive design** optimized for smartphones
- **Intelligent appointment booking** with real-time availability checking
- **Email confirmation system** for both clients and CAF office
- **Multi-service support** (Servizi CAF, Patronato, Avvocato, Sportello Immigrazione)
- **Dynamic employee selection** based on availability
- **Date/time restrictions** (Monday-Friday, business hours)
- **Italian language interface**
- **Accessibility compliant** with semantic HTML
- **Optional contact fields** with "Non registrato" fallback

### **Administrative Features**
- **Comprehensive admin panel** with secure authentication
- **Employee management system** with full CRUD operations
- **Employee availability scheduling** with day-wise and time slot management
- **Special schedule management** for holidays and exceptions
- **Appointment management** with status tracking and filtering
- **Real-time conflict prevention** for double bookings
- **Advanced date filtering** and search capabilities
- **Operator notes** and appointment details management
- **Email notification system** with customizable templates

### **Advanced Scheduling**
- **Employee availability system** with break time management
- **"Disable break" option** for flexible scheduling
- **Special schedules** for holidays and exceptions
- **Real-time availability checking** to prevent conflicts
- **Dynamic employee filtering** based on selected date/time
- **Intelligent appointment suggestions** when conflicts occur

## 🛠️ Tech Stack

- **Next.js 15.3.3** - React framework with App Router
- **Supabase** - Backend-as-a-Service with PostgreSQL database
- **Tailwind CSS v4** - Modern utility-first CSS framework
- **React Hook Form** - Form management and validation
- **Nodemailer** - Email sending functionality
- **bcryptjs** - Password hashing for admin authentication
- **Google Analytics** - Website analytics and tracking
- **React Cookie Consent** - GDPR-compliant cookie management

## 🚀 Getting Started

### Prerequisites

- **Node.js 18+** - JavaScript runtime
- **npm or yarn** - Package manager
- **Supabase account** - Database and backend services
- **Email account** - For sending notifications (Gmail recommended)

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd caf-form
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
cp .env.example .env.local
```

4. **Configure your environment variables in `.env.local`:**
```env
# Supabase Configuration
SUPABASE_URL=your-supabase-project-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
CAF_EMAIL=<EMAIL>

# Admin Authentication
JWT_SECRET=your-secure-jwt-secret-key
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-admin-password

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

**Note for Gmail users:** You need to use an App Password instead of your regular password:
1. Go to Google Account settings
2. Security > 2-Step Verification
3. App passwords > Generate new password
4. Use the generated password in `EMAIL_PASS`

### Database Setup

5. **Set up Supabase database:**
   - Create a new project at [supabase.com](https://supabase.com)
   - Go to SQL Editor in your Supabase dashboard
   - Run the complete database schema (see Database Schema section below)

6. **Initialize the system:**
```bash
# Test Supabase connection
npm run test-supabase

# Run database migrations (if needed)
npm run migrate

# Start development server
npm run dev
```

7. **Open [http://localhost:3000](http://localhost:3000) in your browser.**

### Admin Panel Access

- **URL:** [http://localhost:3000/admin](http://localhost:3000/admin)
- **Username:** Use the `ADMIN_USERNAME` from your environment variables
- **Password:** Use the `ADMIN_PASSWORD` from your environment variables

## 📋 Database Schema

The application uses a comprehensive PostgreSQL database schema with the following main tables:

### Core Tables

**`appointments`** - Main appointment data
- `id` (BIGSERIAL PRIMARY KEY)
- `nome`, `cognome` (VARCHAR) - Client name
- `telefono`, `email` (VARCHAR) - Contact information (optional with "Non registrato" fallback)
- `servizio`, `prestazione` (VARCHAR) - Service details
- `data_appuntamento` (DATE), `orario` (TIME) - Appointment scheduling
- `employee_id` (UUID) - Reference to assigned employee
- `status` (VARCHAR) - Appointment status (confirmed, completed, cancelled, no_show)
- `operator_notes` (TEXT) - Admin notes
- `created_at`, `updated_at` (TIMESTAMP) - Audit fields

**`employees`** - Employee management
- `id` (UUID PRIMARY KEY)
- `nome`, `cognome` (VARCHAR) - Employee name
- `email`, `telefono` (VARCHAR) - Contact information
- `specializzazioni` (TEXT[]) - Array of specializations
- `is_active` (BOOLEAN) - Active status
- `created_at`, `updated_at` (TIMESTAMP) - Audit fields

**`employee_availability`** - Regular weekly schedules
- `id` (UUID PRIMARY KEY)
- `employee_id` (UUID) - Reference to employee
- `day_of_week` (INTEGER) - 1=Monday, 7=Sunday
- `start_time`, `end_time` (TIME) - Work hours
- `break_start`, `break_end` (TIME) - Break times
- `break_disabled` (BOOLEAN) - Disable break option
- `is_active` (BOOLEAN) - Schedule status

**`employee_special_schedules`** - Exceptions and holidays
- `id` (UUID PRIMARY KEY)
- `employee_id` (UUID) - Reference to employee
- `date` (DATE) - Specific date
- `is_available` (BOOLEAN) - Available or not
- `start_time`, `end_time` (TIME) - Custom hours (if available)
- `reason` (VARCHAR) - Reason for exception

**`admin_users`** - Admin authentication
- `id` (UUID PRIMARY KEY)
- `username`, `email` (VARCHAR) - Login credentials
- `password_hash` (VARCHAR) - Hashed password
- `role` (VARCHAR) - Admin role
- `is_active` (BOOLEAN) - Account status

### Security Features
- **Row Level Security (RLS)** enabled on all tables
- **Proper indexes** for performance optimization
- **Foreign key constraints** for data integrity
- **Triggers** for automatic timestamp updates
- **Validation constraints** for data quality

## 📝 Form Fields

### Client Appointment Form

1. **Nome** (First Name) - Required text input
2. **Cognome** (Last Name) - Required text input
3. **Numero Telefono** (Phone) - Optional tel input with validation
4. **Email** - Optional email input with validation
5. **Servizio** (Service) - Dropdown with dynamic options:
   - Servizi CAF
   - Patronato
   - Avvocato
   - Sportello Immigrazione
6. **Prestazione** (Specific Service) - Dynamic dropdown based on selected service
7. **Operatore** (Employee) - Dynamic dropdown showing available employees
8. **Data Appuntamento** (Date) - Date picker (Monday-Friday only)
9. **Orario** (Time) - Time slots based on employee availability:
   - Morning: 09:00 - 13:00 (hourly)
   - Afternoon: 15:00 - 18:00 (hourly)
10. **Note Aggiuntive** (Additional Notes) - Optional text area

### Admin Appointment Form

Includes all client fields plus:
- **Status** - Appointment status selection
- **Operator Notes** - Internal notes field
- **Advanced filtering** and search options

## 🎨 Design System

### Color Palette
- **Primary Red**: #B42C2C (titles, accents)
- **Secondary Blue**: #252B59 (header, footer)
- **Light Background**: #F7F7F5 (main background)
- **Border Gray**: #D1D1D1 (input borders)
- **Primary Text**: #1F1F1F (main text)
- **Secondary Text**: #555555 (supporting text)
- **Hover Accent**: #6B1F1F (button hover)

### Typography
- **Font Family**: System fonts with fallbacks
- **Responsive sizing** with mobile-first approach
- **Accessibility-compliant** contrast ratios

## 📧 Email System

### Automated Email Notifications
When an appointment is submitted:
1. **Client confirmation email** with appointment details and instructions
2. **CAF office notification** with client information and appointment data
3. **HTML-formatted emails** with CAF branding and styling

### Email Features
- **Conditional sending** - Skips email when address is "Non registrato"
- **Template customization** - Easily modify email content and styling
- **Error handling** - Graceful fallback when email fails
- **Multi-language support** - Italian language templates

### Email Configuration
- **SMTP support** for various email providers
- **Gmail integration** with App Password authentication
- **Environment-based configuration** for different deployment environments

## 🏗️ API Endpoints

### Public APIs
- `GET /api/employees` - Get active employees
- `GET /api/employees/available` - Get available employees for date/time
- `POST /api/send-email` - Submit appointment booking
- `GET /api/availability` - Check availability for date/time

### Admin APIs
- `POST /api/admin/login` - Admin authentication
- `GET /api/admin/appointments` - Get appointments with filtering
- `POST /api/admin/appointments/create` - Create appointment
- `PUT /api/admin/appointments` - Update appointment
- `DELETE /api/admin/appointments` - Delete appointment

### Employee Management APIs
- `GET /api/admin/employees` - Get all employees
- `POST /api/admin/employees` - Create employee
- `PUT /api/admin/employees/[id]` - Update employee
- `DELETE /api/admin/employees/[id]` - Delete employee
- `GET /api/admin/employees/[id]/availability` - Get employee availability
- `POST /api/admin/employees/[id]/availability` - Set employee availability
- `GET /api/admin/employees/[id]/special-schedules` - Get special schedules
- `POST /api/admin/employees/[id]/special-schedules` - Create special schedule

## 🚀 Deployment

### Vercel (Recommended)

1. **Push your code to GitHub**
2. **Connect your repository to Vercel**
3. **Add environment variables in Vercel dashboard:**
   - All variables from `.env.local`
   - Ensure Supabase URLs and keys are correct
4. **Deploy and test**

### Other Platforms

Ensure your hosting platform supports:
- **Node.js 18+** - Runtime environment
- **Environment variables** - Configuration management
- **API routes** - Server-side functionality
- **PostgreSQL database** - Via Supabase or direct connection

### Environment Variables for Production
```env
# Required for all deployments
SUPABASE_URL=your-production-supabase-url
SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-key
EMAIL_USER=your-production-email
EMAIL_PASS=your-production-email-password
CAF_EMAIL=your-caf-office-email
JWT_SECRET=your-secure-production-jwt-secret
ADMIN_USERNAME=your-admin-username
ADMIN_PASSWORD=your-secure-admin-password

# Optional
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

## 🎛️ Customization

### Logo and Branding
- Replace logo in `public/img/logo/logo.png`
- Update favicon in `app/favicon.ico`
- Modify colors in `app/globals.css`

### Email Templates
- Customize templates in `app/api/send-email/route.js`
- Update email styling and content
- Modify sender information

### Services and Prestazioni
- Update service options in database or admin panel
- Modify service-specific logic in components
- Add new service categories as needed

### Employee Specializations
- Configure employee specializations in admin panel
- Update availability rules and constraints
- Customize employee selection logic

## 🧪 Testing

### Automated Testing
The project includes comprehensive test scripts:

```bash
# Test Supabase connection
npm run test-supabase

# Test employee system
node scripts/test-employee-system.js

# Test availability system
node scripts/test-availability-system.js

# Test appointment operations
node scripts/test-appointment-operations.js

# Test admin operations
node scripts/test-admin-operations.js
```

### Manual Testing Checklist

#### Client-Side Testing
- [ ] Book appointment with all required fields
- [ ] Book appointment with optional fields empty
- [ ] Test employee selection based on availability
- [ ] Test date/time restrictions
- [ ] Test email confirmation system
- [ ] Test mobile responsiveness

#### Admin Panel Testing
- [ ] Admin login functionality
- [ ] Employee management (CRUD operations)
- [ ] Employee availability configuration
- [ ] Special schedule management
- [ ] Appointment management and filtering
- [ ] Status updates and operator notes

#### Integration Testing
- [ ] Database operations and data integrity
- [ ] Email system with various scenarios
- [ ] Availability checking and conflict prevention
- [ ] Real-time updates and synchronization

## 🔧 Troubleshooting

### Common Issues

**Database Connection Errors:**
- Verify Supabase URL and API keys in environment variables
- Check Supabase project status and billing
- Ensure database schema is properly set up

**Email Not Sending:**
- Verify email credentials and App Password setup
- Check email provider settings and security
- Review email logs in application console

**Admin Login Issues:**
- Verify JWT_SECRET is set correctly
- Check admin user exists in database
- Ensure password hash is generated properly

**Appointment Booking Failures:**
- Check availability system configuration
- Verify employee schedules are set up
- Review browser console for JavaScript errors

**Employee Availability Issues:**
- Ensure employee availability is configured
- Check special schedules for conflicts
- Verify time zone settings and date formats

### Debug Mode
Enable detailed logging by setting:
```env
NODE_ENV=development
```

### Getting Help
1. Check browser console for error messages
2. Review server logs for API errors
3. Test database operations directly
4. Verify environment variable configuration
5. Run automated test scripts

## 📚 Development Guide

### Project Structure
```
caf-form/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin panel pages
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── AdminBookingForm.js
│   ├── AppointmentForm.js
│   ├── EmployeeManagement.js
│   └── ...
├── lib/                   # Utility libraries
│   ├── supabase.js       # Supabase client
│   ├── supabaseEmployeeUtils.js
│   ├── supabaseAvailabilityUtils.js
│   └── ...
├── public/               # Static assets
└── scripts/              # Utility scripts
```

### Key Components
- **AppointmentForm.js** - Main booking form with availability checking
- **AdminBookingForm.js** - Admin appointment creation form
- **EmployeeManagement.js** - Employee CRUD interface
- **EmployeeAvailabilityManagement.js** - Availability scheduling interface

### Database Utilities
- **supabaseEmployeeUtils.js** - Employee operations
- **supabaseAvailabilityUtils.js** - Availability management
- **supabaseAppointmentUtils.js** - Appointment operations
- **supabaseAuthUtils.js** - Authentication utilities

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Make your changes** following the existing code style
4. **Add tests** for new functionality
5. **Test thoroughly** using the provided test scripts
6. **Commit your changes** (`git commit -m 'Add amazing feature'`)
7. **Push to the branch** (`git push origin feature/amazing-feature`)
8. **Open a Pull Request**

### Development Guidelines
- Follow existing code patterns and naming conventions
- Add comprehensive comments for complex logic
- Update documentation for new features
- Ensure backward compatibility when possible
- Test all changes thoroughly before submitting

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For technical support or questions about the CAF appointment system:

1. **Check this documentation** for common solutions
2. **Run the test scripts** to diagnose issues
3. **Review the troubleshooting section** above
4. **Check browser console** for error messages
5. **Contact the development team** for additional support

### Support Resources
- **Documentation**: This comprehensive README
- **Test Scripts**: Automated testing and validation
- **Error Logging**: Detailed error messages and debugging
- **Community**: GitHub Issues for bug reports and feature requests

---

**Built with ❤️ for Centro di Assistenza Fiscale (CAF)**
