import { NextResponse } from 'next/server';
import { authenticateAdmin } from '../../../../../../lib/supabaseAuthUtils.js';
import { 
  getEmployeeAvailability, 
  setEmployeeAvailability,
  getEmployeeSpecialSchedules,
  setEmployeeSpecialSchedule,
  deleteEmployeeSpecialSchedule
} from '../../../../../../lib/supabaseAvailabilityUtils.js';

/**
 * GET /api/admin/employees/[id]/availability
 * Get employee availability and special schedules
 */
export async function GET(request, { params }) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { id: employeeId } = await params;
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    if (!employeeId) {
      return NextResponse.json(
        { success: false, message: 'ID dipendente obbligatorio' },
        { status: 400 }
      );
    }

    // Get regular availability
    const availability = await getEmployeeAvailability(employeeId);

    // Get special schedules
    const specialSchedules = await getEmployeeSpecialSchedules(employeeId, startDate, endDate);

    return NextResponse.json({
      success: true,
      data: {
        employeeId,
        availability,
        specialSchedules
      }
    });

  } catch (error) {
    console.error('Error fetching employee availability:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore interno del server',
        error: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/employees/[id]/availability
 * Update employee availability
 */
export async function PUT(request, { params }) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { id: employeeId } = await params;
    const { availability } = await request.json();

    // Debug logging
    console.log('🔍 DEBUG - Employee ID:', employeeId);
    console.log('🔍 DEBUG - Availability data:', JSON.stringify(availability, null, 2));

    if (!employeeId) {
      return NextResponse.json(
        { success: false, message: 'ID dipendente obbligatorio' },
        { status: 400 }
      );
    }

    if (!Array.isArray(availability)) {
      return NextResponse.json(
        { success: false, message: 'Dati di disponibilità non validi' },
        { status: 400 }
      );
    }

    // Validate availability data
    for (const avail of availability) {
      console.log('🔍 DEBUG - Validating availability record:', JSON.stringify(avail, null, 2));

      if (typeof avail.day_of_week !== 'number' || avail.day_of_week < 0 || avail.day_of_week > 6) {
        console.log('❌ DEBUG - Invalid day_of_week:', avail.day_of_week);
        return NextResponse.json(
          { success: false, message: 'Giorno della settimana non valido' },
          { status: 400 }
        );
      }

      if (!avail.start_time || !avail.end_time) {
        console.log('❌ DEBUG - Missing start_time or end_time:', { start_time: avail.start_time, end_time: avail.end_time });
        return NextResponse.json(
          { success: false, message: 'Orari di inizio e fine obbligatori' },
          { status: 400 }
        );
      }

      // Validate time format (HH:MM or HH:MM:SS)
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
      console.log('🔍 DEBUG - Testing time format:', { start_time: avail.start_time, end_time: avail.end_time });
      console.log('🔍 DEBUG - Regex test results:', {
        start_time_valid: timeRegex.test(avail.start_time),
        end_time_valid: timeRegex.test(avail.end_time)
      });

      if (!timeRegex.test(avail.start_time) || !timeRegex.test(avail.end_time)) {
        console.log('❌ DEBUG - Invalid time format detected');
        return NextResponse.json(
          { success: false, message: `Formato orario non valido (usa HH:MM). Start: "${avail.start_time}", End: "${avail.end_time}"` },
          { status: 400 }
        );
      }

      // Validate that end time is after start time
      if (avail.end_time <= avail.start_time) {
        return NextResponse.json(
          { success: false, message: 'L\'orario di fine deve essere successivo all\'orario di inizio' },
          { status: 400 }
        );
      }

      // Validate break times if provided and break is not disabled
      if (!avail.break_disabled) {
        // Only validate break times if they are actually provided (not empty strings)
        const hasBreakStart = avail.break_start_time && avail.break_start_time.trim() !== '';
        const hasBreakEnd = avail.break_end_time && avail.break_end_time.trim() !== '';

        if (hasBreakStart || hasBreakEnd) {
          if (hasBreakStart && hasBreakEnd) {
            console.log('🔍 DEBUG - Testing break time format:', {
              break_start_time: avail.break_start_time,
              break_end_time: avail.break_end_time
            });
            if (!timeRegex.test(avail.break_start_time) || !timeRegex.test(avail.break_end_time)) {
              console.log('❌ DEBUG - Invalid break time format detected');
              return NextResponse.json(
                { success: false, message: 'Formato orario pausa non valido (usa HH:MM)' },
                { status: 400 }
              );
            }

            if (avail.break_end_time <= avail.break_start_time) {
              return NextResponse.json(
                { success: false, message: 'L\'orario di fine pausa deve essere successivo all\'orario di inizio pausa' },
                { status: 400 }
              );
            }

            if (avail.break_start_time < avail.start_time || avail.break_end_time > avail.end_time) {
              return NextResponse.json(
                { success: false, message: 'Gli orari di pausa devono essere compresi nell\'orario di lavoro' },
                { status: 400 }
              );
            }
          } else {
            return NextResponse.json(
              { success: false, message: 'Specificare sia l\'orario di inizio che di fine pausa' },
              { status: 400 }
            );
          }
        }
      }
    }

    // Update availability
    const updatedAvailability = await setEmployeeAvailability(employeeId, availability);

    return NextResponse.json({
      success: true,
      message: 'Disponibilità aggiornata con successo',
      data: updatedAvailability
    });

  } catch (error) {
    console.error('Error updating employee availability:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore interno del server',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
