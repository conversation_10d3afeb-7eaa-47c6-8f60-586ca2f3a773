'use client';

import { useState, useEffect } from 'react';

export default function EditAppointmentModal({ appointment, isOpen, onClose, onSave, token }) {
  const [formData, setFormData] = useState({
    nome: '',
    cognome: '',
    telefono: '',
    email: '',
    servizio: '',
    prestazione: '',
    operatore: '',
    noteAggiuntive: '',
    dataAppuntamento: '',
    orario: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [submitError, setSubmitError] = useState('');

  // Initialize form data when appointment changes
  useEffect(() => {
    if (appointment) {
      setFormData({
        nome: appointment.nome || '',
        cognome: appointment.cognome || '',
        telefono: appointment.telefono || '',
        email: appointment.email || '',
        servizio: appointment.servizio || '',
        prestazione: appointment.prestazione || '',
        operatore: appointment.operatore || '',
        noteAggiuntive: appointment.noteAggiuntive || '',
        dataAppuntamento: appointment.dataAppuntamento || '',
        orario: appointment.orario || ''
      });
    }
  }, [appointment]);

  // Close modal on Escape key press
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Reset messages when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSubmitMessage('');
      setSubmitError('');
    }
  }, [isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear messages when user starts typing
    if (submitMessage || submitError) {
      setSubmitMessage('');
      setSubmitError('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage('');
    setSubmitError('');

    try {
      // Validate required fields
      if (!formData.cognome.trim()) {
        throw new Error('Cognome è obbligatorio');
      }
      if (!formData.telefono.trim()) {
        throw new Error('Telefono è obbligatorio');
      }
      if (!formData.servizio.trim()) {
        throw new Error('Servizio è obbligatorio');
      }
      if (!formData.dataAppuntamento) {
        throw new Error('Data appuntamento è obbligatoria');
      }
      if (!formData.orario) {
        throw new Error('Orario è obbligatorio');
      }

      // Validate date is not in the past
      const appointmentDate = new Date(formData.dataAppuntamento);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (appointmentDate < today) {
        throw new Error('Non è possibile prenotare appuntamenti nel passato');
      }

      // Note: Email validation removed as requested - email is optional
      // Email field accepts any value including "Non registrato" without validation

      console.log('Submitting appointment update:', formData);

      const response = await fetch(`/api/admin/appointments/${appointment.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSubmitMessage('Appuntamento aggiornato con successo!');

        // Call the onSave callback with updated appointment data
        if (onSave) {
          onSave(result.data);
        }

        // Close modal after a short delay
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        // Handle specific error codes for better user experience
        if (result.code === 'TIME_SLOT_UNAVAILABLE_EMPLOYEE') {
          // Use the detailed message from the server if available
          const errorMessage = result.message || 'L\'orario selezionato non è disponibile per il dipendente specificato.';
          throw new Error(errorMessage);
        } else if (result.code === 'TIME_SLOT_UNAVAILABLE') {
          throw new Error(result.message || 'L\'orario selezionato non è più disponibile.');
        } else {
          throw new Error(result.message || 'Errore durante l\'aggiornamento');
        }
      }
    } catch (error) {
      console.error('Error updating appointment:', error);
      setSubmitError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen || !appointment) return null;

  // Format date for display in header
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-[var(--primary-red)] text-white p-6 rounded-t-lg">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold">Modifica Appuntamento</h2>
              <p className="text-red-200 mt-1">ID: {appointment.id}</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-300 text-2xl font-bold"
              aria-label="Chiudi"
            >
              ×
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Success/Error Messages */}
          {submitMessage && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              {submitMessage}
            </div>
          )}
          {submitError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {submitError}
            </div>
          )}

          {/* Client Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-[var(--primary-text)] mb-4 flex items-center">
              <span className="mr-2">👤</span>
              Informazioni Cliente
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Nome
                </label>
                <input
                  type="text"
                  name="nome"
                  value={formData.nome}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                  placeholder="Nome (opzionale)"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Cognome *
                </label>
                <input
                  type="text"
                  name="cognome"
                  value={formData.cognome}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                  placeholder="Cognome"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Telefono *
                </label>
                <input
                  type="tel"
                  name="telefono"
                  value={formData.telefono}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                  placeholder="Numero di telefono"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Email
                </label>
                <input
                  type="text"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                  placeholder="Email (opzionale)"
                />
              </div>
            </div>
          </div>

          {/* Appointment Information */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="text-lg font-semibold text-[var(--primary-text)] mb-4 flex items-center">
              <span className="mr-2">📅</span>
              Dettagli Appuntamento
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Data *
                </label>
                <input
                  type="date"
                  name="dataAppuntamento"
                  value={formData.dataAppuntamento}
                  onChange={handleInputChange}
                  required
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Orario *
                </label>
                <input
                  type="time"
                  name="orario"
                  value={formData.orario}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Service Information */}
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h3 className="text-lg font-semibold text-[var(--primary-text)] mb-4 flex items-center">
              <span className="mr-2">🏢</span>
              Servizio
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Servizio *
                </label>
                <input
                  type="text"
                  name="servizio"
                  value={formData.servizio}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                  placeholder="Servizio richiesto"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Prestazione
                </label>
                <input
                  type="text"
                  name="prestazione"
                  value={formData.prestazione}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                  placeholder="Prestazione specifica (opzionale)"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Operatore
                </label>
                <input
                  type="text"
                  name="operatore"
                  value={formData.operatore}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                  placeholder="Operatore preferito (opzionale)"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--secondary-text)] mb-1">
                  Note Aggiuntive
                </label>
                <textarea
                  name="noteAggiuntive"
                  value={formData.noteAggiuntive}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
                  placeholder="Note aggiuntive (opzionale)"
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Annulla
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-[var(--primary-red)] text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Aggiornamento...
                </>
              ) : (
                'Salva Modifiche'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
