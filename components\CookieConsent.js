'use client';

import { useState, useEffect } from 'react';
import {
  shouldShowConsentBanner,
  acceptAllCookies,
  rejectAllCookies,
  saveCookieConsent,
  getConsentCategories,
  getCookieConsent
} from '../lib/consent';

export default function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState({
    necessary: true,
    analytics: false,
    marketing: false,
    preferences: false,
  });

  useEffect(() => {
    // Check if banner should be shown
    setShowBanner(shouldShowConsentBanner());
    
    // Load existing preferences if any
    const existingConsent = getCookieConsent();
    if (existingConsent) {
      setPreferences(existingConsent);
    }
  }, []);

  const handleAcceptAll = () => {
    acceptAllCookies();
    setShowBanner(false);
  };

  const handleRejectAll = () => {
    rejectAllCookies();
    setShowBanner(false);
  };

  const handleSavePreferences = () => {
    saveCookieConsent(preferences);
    setShowBanner(false);
    setShowDetails(false);
  };

  const handlePreferenceChange = (category, value) => {
    setPreferences(prev => ({
      ...prev,
      [category]: value
    }));
  };

  if (!showBanner) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center p-4 pointer-events-none">
      <div className="bg-white border border-[var(--border-gray)] rounded-lg shadow-2xl max-w-4xl w-full pointer-events-auto">
        {!showDetails ? (
          // Simple Banner
          <div className="p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-[var(--primary-red)] rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-[var(--primary-text)] mb-2">
                  Utilizziamo i Cookie
                </h3>
                <p className="text-[var(--secondary-text)] text-sm mb-4">
                  Utilizziamo cookie e tecnologie simili per migliorare la tua esperienza sul nostro sito, 
                  analizzare il traffico e personalizzare i contenuti. Puoi scegliere quali cookie accettare.
                </p>
                
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={handleAcceptAll}
                    className="bg-[var(--primary-red)] text-white px-4 py-2 rounded-lg hover:bg-[var(--hover-accent)] transition-colors text-sm font-medium"
                  >
                    Accetta Tutti
                  </button>
                  
                  <button
                    onClick={handleRejectAll}
                    className="bg-gray-200 text-[var(--primary-text)] px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors text-sm font-medium"
                  >
                    Rifiuta Tutti
                  </button>
                  
                  <button
                    onClick={() => setShowDetails(true)}
                    className="border border-[var(--border-gray)] text-[var(--primary-text)] px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium"
                  >
                    Personalizza
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Detailed Preferences
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-[var(--primary-text)]">
                Preferenze Cookie
              </h3>
              <button
                onClick={() => setShowDetails(false)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-4 mb-6">
              {getConsentCategories().map((category) => (
                <div key={category.id} className="border border-[var(--border-gray)] rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-[var(--primary-text)]">
                      {category.name}
                    </h4>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={preferences[category.id]}
                        disabled={category.required}
                        onChange={(e) => handlePreferenceChange(category.id, e.target.checked)}
                        className="sr-only"
                      />
                      <div className={`w-11 h-6 rounded-full transition-colors ${
                        preferences[category.id] 
                          ? 'bg-[var(--primary-red)]' 
                          : 'bg-gray-300'
                      } ${category.required ? 'opacity-50' : ''}`}>
                        <div className={`w-5 h-5 bg-white rounded-full shadow transform transition-transform ${
                          preferences[category.id] ? 'translate-x-5' : 'translate-x-0'
                        } mt-0.5 ml-0.5`}></div>
                      </div>
                    </label>
                  </div>
                  <p className="text-sm text-[var(--secondary-text)]">
                    {category.description}
                  </p>
                  {category.required && (
                    <p className="text-xs text-gray-500 mt-1">
                      Sempre attivo - Necessario per il funzionamento del sito
                    </p>
                  )}
                </div>
              ))}
            </div>
            
            <div className="flex flex-wrap gap-3">
              <button
                onClick={handleSavePreferences}
                className="bg-[var(--primary-red)] text-white px-6 py-2 rounded-lg hover:bg-[var(--hover-accent)] transition-colors font-medium"
              >
                Salva Preferenze
              </button>
              
              <button
                onClick={handleAcceptAll}
                className="border border-[var(--primary-red)] text-[var(--primary-red)] px-6 py-2 rounded-lg hover:bg-red-50 transition-colors font-medium"
              >
                Accetta Tutti
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
