'use client';

import { GoogleAnalytics as GA } from '@next/third-parties/google';
import { useEffect } from 'react';
import { getCookieConsent } from '../lib/consent';

export default function GoogleAnalytics() {
  const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

  useEffect(() => {
    // Initialize Google Consent Mode
    if (typeof window !== 'undefined' && window.gtag) {
      // Set default consent state
      window.gtag('consent', 'default', {
        analytics_storage: 'denied',
        ad_storage: 'denied',
        ad_user_data: 'denied',
        ad_personalization: 'denied',
        functionality_storage: 'granted',
        security_storage: 'granted',
      });

      // Check if user has already given consent
      const consent = getCookieConsent();
      if (consent) {
        updateConsent(consent);
      }
    }
  }, []);

  const updateConsent = (consent) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: consent.analytics ? 'granted' : 'denied',
        ad_storage: consent.marketing ? 'granted' : 'denied',
        ad_user_data: consent.marketing ? 'granted' : 'denied',
        ad_personalization: consent.marketing ? 'granted' : 'denied',
      });
    }
  };

  // Expose updateConsent function globally for the consent banner
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.updateGAConsent = updateConsent;
    }
  }, []);

  if (!gaId || gaId === 'G-XXXXXXXXXX') {
    console.warn('Google Analytics Measurement ID not configured');
    return null;
  }

  return <GA gaId={gaId} />;
}
