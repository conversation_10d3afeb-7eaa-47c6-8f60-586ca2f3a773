-- ========================================
-- CAF APPOINTMENT SYSTEM - CUSTOM TIME RANGES MIGRATION
-- ========================================
-- This migration adds support for custom time ranges in appointments
-- while maintaining backward compatibility with existing predefined slots
--
-- Changes:
-- 1. Add start_time and end_time columns to appointments table
-- 2. Add custom_time_range boolean flag
-- 3. Update indexes for performance
-- 4. Maintain backward compatibility with existing orario field
-- ========================================

-- Add new columns to appointments table
ALTER TABLE appointments 
ADD COLUMN IF NOT EXISTS start_time TIME,
ADD COLUMN IF NOT EXISTS end_time TIME,
ADD COLUMN IF NOT EXISTS custom_time_range BOOLEAN DEFAULT false;

-- Add comments for new columns
COMMENT ON COLUMN appointments.start_time IS 'Start time for custom time range appointments (optional)';
COMMENT ON COLUMN appointments.end_time IS 'End time for custom time range appointments (optional)';
COMMENT ON COLUMN appointments.custom_time_range IS 'Flag indicating if this appointment uses custom time range (true) or predefined slot (false)';

-- Add check constraint to ensure data integrity
ALTER TABLE appointments 
ADD CONSTRAINT check_time_range_consistency 
CHECK (
    (custom_time_range = false AND orario IS NOT NULL AND start_time IS NULL AND end_time IS NULL) OR
    (custom_time_range = true AND start_time IS NOT NULL AND end_time IS NOT NULL AND end_time > start_time)
);

-- Add indexes for performance on new columns
CREATE INDEX IF NOT EXISTS idx_appointments_custom_time_range ON appointments(custom_time_range);
CREATE INDEX IF NOT EXISTS idx_appointments_start_end_time ON appointments(data_appuntamento, start_time, end_time) 
WHERE custom_time_range = true;

-- Update existing appointments to set custom_time_range = false (they use predefined slots)
UPDATE appointments 
SET custom_time_range = false 
WHERE custom_time_range IS NULL;

-- Create a function to check for time range conflicts
CREATE OR REPLACE FUNCTION check_appointment_time_conflicts(
    p_date DATE,
    p_start_time TIME,
    p_end_time TIME,
    p_exclude_id BIGINT DEFAULT NULL
) RETURNS TABLE (
    conflicting_appointment_id BIGINT,
    conflict_type TEXT,
    conflict_time TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id as conflicting_appointment_id,
        CASE 
            WHEN a.custom_time_range = true THEN 'custom_range'
            ELSE 'predefined_slot'
        END as conflict_type,
        CASE 
            WHEN a.custom_time_range = true THEN a.start_time::TEXT || '-' || a.end_time::TEXT
            ELSE a.orario::TEXT
        END as conflict_time
    FROM appointments a
    WHERE a.data_appuntamento = p_date
        AND a.status != 'cancelled'
        AND (p_exclude_id IS NULL OR a.id != p_exclude_id)
        AND (
            -- Check overlap with custom time range appointments
            (a.custom_time_range = true AND 
             p_start_time < a.end_time AND p_end_time > a.start_time)
            OR
            -- Check overlap with predefined slot appointments (assume 30-minute duration)
            (a.custom_time_range = false AND 
             p_start_time < (a.orario + INTERVAL '30 minutes')::TIME AND 
             p_end_time > a.orario)
        );
END;
$$ LANGUAGE plpgsql;

-- Create a function to validate appointment time data
CREATE OR REPLACE FUNCTION validate_appointment_time_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate custom time range appointments
    IF NEW.custom_time_range = true THEN
        -- Ensure start_time and end_time are provided
        IF NEW.start_time IS NULL OR NEW.end_time IS NULL THEN
            RAISE EXCEPTION 'start_time and end_time are required for custom time range appointments';
        END IF;
        
        -- Ensure end_time is after start_time
        IF NEW.end_time <= NEW.start_time THEN
            RAISE EXCEPTION 'end_time must be after start_time for custom time range appointments';
        END IF;
        
        -- Ensure times are within business hours (9 AM - 8 PM) - Extended for employees with extended availability
        IF NEW.start_time < '09:00'::TIME OR NEW.end_time > '20:00'::TIME THEN
            RAISE EXCEPTION 'Appointment times must be between 09:00 and 20:00';
        END IF;
        
        -- Clear orario for custom time range appointments
        NEW.orario := NULL;
        
    ELSE
        -- Validate predefined slot appointments
        IF NEW.orario IS NULL THEN
            RAISE EXCEPTION 'orario is required for predefined slot appointments';
        END IF;
        
        -- Clear custom time fields for predefined slot appointments
        NEW.start_time := NULL;
        NEW.end_time := NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to validate appointment time data
DROP TRIGGER IF EXISTS trigger_validate_appointment_time_data ON appointments;
CREATE TRIGGER trigger_validate_appointment_time_data
    BEFORE INSERT OR UPDATE ON appointments
    FOR EACH ROW
    EXECUTE FUNCTION validate_appointment_time_data();

-- Update the unique constraint to handle both types of appointments
-- First, drop the existing constraint if it exists
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS unique_appointment_slot;

-- Create a new unique constraint that handles both predefined slots and custom time ranges
-- For predefined slots: unique on (data_appuntamento, orario) where custom_time_range = false
-- For custom ranges: we'll rely on the conflict checking function rather than a strict unique constraint
-- since custom ranges can potentially overlap in complex ways

CREATE UNIQUE INDEX IF NOT EXISTS unique_predefined_appointment_slot 
ON appointments (data_appuntamento, orario) 
WHERE custom_time_range = false AND status != 'cancelled';

-- Add a comment explaining the time range system
COMMENT ON TABLE appointments IS 'Main appointments table supporting both predefined time slots and custom time ranges. Use custom_time_range flag to determine appointment type.';

-- Create a view for easier querying of appointment times
CREATE OR REPLACE VIEW appointment_times_view AS
SELECT 
    id,
    nome,
    cognome,
    data_appuntamento,
    custom_time_range,
    CASE 
        WHEN custom_time_range = true THEN start_time::TEXT || '-' || end_time::TEXT
        ELSE orario::TEXT
    END as display_time,
    CASE 
        WHEN custom_time_range = true THEN start_time
        ELSE orario
    END as effective_start_time,
    CASE 
        WHEN custom_time_range = true THEN end_time
        ELSE (orario + INTERVAL '30 minutes')::TIME
    END as effective_end_time,
    status,
    servizio,
    prestazione,
    operatore,
    created_at
FROM appointments
WHERE status != 'cancelled'
ORDER BY data_appuntamento, effective_start_time;

-- Grant permissions on new view
GRANT SELECT ON appointment_times_view TO anon, authenticated;

-- Display migration completion message
DO $$
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE 'CUSTOM TIME RANGES MIGRATION COMPLETED!';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Changes applied:';
    RAISE NOTICE '- Added start_time, end_time, custom_time_range columns';
    RAISE NOTICE '- Added validation constraints and triggers';
    RAISE NOTICE '- Created conflict checking function';
    RAISE NOTICE '- Updated indexes for performance';
    RAISE NOTICE '- Created appointment_times_view for easier querying';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Backward compatibility maintained:';
    RAISE NOTICE '- Existing appointments continue to work with orario field';
    RAISE NOTICE '- New custom time range appointments use start_time/end_time';
    RAISE NOTICE '========================================';
END $$;
