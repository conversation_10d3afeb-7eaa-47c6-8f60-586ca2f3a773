import { NextResponse } from 'next/server';
import { authenticateAdmin } from '../../../../../lib/supabaseAuthUtils.js';
import { updateAppointment } from '../../../../../lib/supabaseAppointmentUtils.js';

/**
 * PATCH /api/admin/appointments/[id]
 * Update appointment details (date, time, and other fields)
 */
export async function PATCH(request, { params }) {
  try {
    console.log('PATCH /api/admin/appointments/[id] - Update appointment request received');

    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      console.log('Authentication failed');
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    // Get appointment ID from params
    const { id: appointmentId } = await params;
    console.log('Appointment ID from params:', appointmentId);

    if (!appointmentId) {
      return NextResponse.json(
        { success: false, message: 'ID appuntamento obbligatorio' },
        { status: 400 }
      );
    }

    // Get update data from request body
    const updateData = await request.json();
    console.log('Update data received:', updateData);

    // Validate required fields if they are being updated
    if (updateData.cognome !== undefined && !updateData.cognome?.trim()) {
      return NextResponse.json(
        { success: false, message: 'Cognome è obbligatorio' },
        { status: 400 }
      );
    }

    if (updateData.telefono !== undefined && !updateData.telefono?.trim()) {
      return NextResponse.json(
        { success: false, message: 'Telefono è obbligatorio' },
        { status: 400 }
      );
    }

    if (updateData.servizio !== undefined && !updateData.servizio?.trim()) {
      return NextResponse.json(
        { success: false, message: 'Servizio è obbligatorio' },
        { status: 400 }
      );
    }

    if (updateData.dataAppuntamento !== undefined && !updateData.dataAppuntamento) {
      return NextResponse.json(
        { success: false, message: 'Data appuntamento è obbligatoria' },
        { status: 400 }
      );
    }

    if (updateData.orario !== undefined && !updateData.orario) {
      return NextResponse.json(
        { success: false, message: 'Orario è obbligatorio' },
        { status: 400 }
      );
    }

    // Validate date format if provided
    if (updateData.dataAppuntamento) {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(updateData.dataAppuntamento)) {
        return NextResponse.json(
          { success: false, message: 'Formato data non valido (YYYY-MM-DD)' },
          { status: 400 }
        );
      }

      // Check if date is not in the past
      const appointmentDate = new Date(updateData.dataAppuntamento);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (appointmentDate < today) {
        return NextResponse.json(
          { success: false, message: 'Non è possibile prenotare appuntamenti nel passato' },
          { status: 400 }
        );
      }
    }

    // Validate time format if provided
    if (updateData.orario) {
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(updateData.orario)) {
        return NextResponse.json(
          { success: false, message: 'Formato orario non valido (HH:MM)' },
          { status: 400 }
        );
      }
    }

    // Call the update function
    console.log(`Calling updateAppointment with ID: ${appointmentId}`);
    const updatedAppointment = await updateAppointment(appointmentId, updateData);
    console.log('Update successful:', updatedAppointment);

    return NextResponse.json({
      success: true,
      message: 'Appuntamento aggiornato con successo',
      data: updatedAppointment
    });

  } catch (error) {
    console.error('Error updating appointment:', error);

    // Handle specific error types
    if (error.message === 'TIME_SLOT_UNAVAILABLE' || error.message === 'TIME_SLOT_UNAVAILABLE_EMPLOYEE') {
      // Use detailed message if available, otherwise use default
      const errorMessage = error.detailedMessage || (
        error.message === 'TIME_SLOT_UNAVAILABLE_EMPLOYEE'
          ? 'L\'orario selezionato non è più disponibile per il dipendente specificato. Un altro appuntamento è già prenotato per questo dipendente in questo orario.'
          : 'L\'orario selezionato non è più disponibile. Un altro appuntamento è già prenotato per questo orario.'
      );

      return NextResponse.json(
        {
          success: false,
          message: errorMessage,
          code: error.message === 'TIME_SLOT_UNAVAILABLE_EMPLOYEE' ? 'TIME_SLOT_UNAVAILABLE_EMPLOYEE' : 'TIME_SLOT_UNAVAILABLE',
          // Include additional details for better UX
          employeeName: error.employeeName,
          conflictTime: error.conflictTime,
          conflictDate: error.conflictDate
        },
        { status: 409 }
      );
    }

    if (error.message.includes('No appointment found')) {
      return NextResponse.json(
        { success: false, message: 'Appuntamento non trovato' },
        { status: 404 }
      );
    }

    if (error.message.includes('Invalid') || error.message.includes('obbligatorio')) {
      return NextResponse.json(
        { success: false, message: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, message: 'Errore del server durante l\'aggiornamento', error: error.message },
      { status: 500 }
    );
  }
}
