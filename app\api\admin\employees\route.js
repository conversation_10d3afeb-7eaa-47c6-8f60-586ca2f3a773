import { NextResponse } from 'next/server';
import { authenticateAdmin } from '../../../../lib/supabaseAuthUtils.js';
import { 
  getEmployees, 
  createEmployee, 
  getEmployeeStats 
} from '../../../../lib/supabaseEmployeeUtils.js';

/**
 * GET /api/admin/employees
 * Get all employees or employee statistics
 */
export async function GET(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('active') === 'true';
    const stats = searchParams.get('stats') === 'true';

    if (stats) {
      // Return employee statistics
      const employeeStats = await getEmployeeStats();
      return NextResponse.json({
        success: true,
        data: employeeStats
      });
    } else {
      // Return list of employees
      const employees = await getEmployees(activeOnly);
      return NextResponse.json({
        success: true,
        data: employees
      });
    }
  } catch (error) {
    console.error('Error in GET /api/admin/employees:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore interno del server',
        error: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/employees
 * Create a new employee
 */
export async function POST(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const employeeData = await request.json();

    // Validate required fields
    if (!employeeData.name || employeeData.name.trim() === '') {
      return NextResponse.json(
        { success: false, message: 'Il nome del dipendente è obbligatorio' },
        { status: 400 }
      );
    }

    // Validate email format if provided
    if (employeeData.email && employeeData.email.trim() !== '') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(employeeData.email)) {
        return NextResponse.json(
          { success: false, message: 'Formato email non valido' },
          { status: 400 }
        );
      }
    }

    // Create the employee
    const newEmployee = await createEmployee(employeeData);

    return NextResponse.json({
      success: true,
      message: 'Dipendente creato con successo',
      data: newEmployee
    }, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/admin/employees:', error);
    
    // Handle specific database errors
    if (error.message.includes('duplicate key value violates unique constraint')) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Un dipendente con questa email esiste già' 
        },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore nella creazione del dipendente',
        error: error.message 
      },
      { status: 500 }
    );
  }
}
