'use client';

import { useState, useEffect } from 'react';

export default function EmployeeList({
  token,
  onEditEmployee,
  onDeleteEmployee,
  onCreateEmployee,
  refreshTrigger
}) {
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all'); // all, active, inactive
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      setLoading(true);
      setError('');

      const activeOnly = filter === 'active';
      const url = `/api/admin/employees${activeOnly ? '?active=true' : ''}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Errore nel caricamento dei dipendenti');
      }

      let filteredEmployees = result.data || [];

      // Apply inactive filter if needed
      if (filter === 'inactive') {
        filteredEmployees = filteredEmployees.filter(emp => !emp.is_active);
      }

      setEmployees(filteredEmployees);
    } catch (error) {
      console.error('Error fetching employees:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch employees on component mount and when dependencies change
  useEffect(() => {
    if (token) {
      fetchEmployees();
    }
  }, [token, filter, refreshTrigger]);

  // Filter employees by search term
  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.email && employee.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (employee.role && employee.role.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (employee.department && employee.department.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleDelete = async (employee) => {
    if (employee.name === 'Qualsiasi') {
      alert('Non è possibile eliminare l\'operatore "Qualsiasi"');
      return;
    }

    const confirmMessage = `Sei sicuro di voler eliminare il dipendente "${employee.name}"?\n\nSe il dipendente ha appuntamenti associati, verrà disattivato invece di essere eliminato.`;
    
    if (window.confirm(confirmMessage)) {
      try {
        const response = await fetch(`/api/admin/employees/${employee.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.message || 'Errore nell\'eliminazione del dipendente');
        }

        // Refresh the list
        fetchEmployees();
        
        if (onDeleteEmployee) {
          onDeleteEmployee(employee);
        }

        alert('Dipendente eliminato con successo');
      } catch (error) {
        console.error('Error deleting employee:', error);
        alert(`Errore: ${error.message}`);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-red)]"></div>
        <span className="ml-2">Caricamento dipendenti...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Create Button */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-[var(--primary-text)]">
          Gestione Dipendenti
        </h2>
        <button
          onClick={onCreateEmployee}
          className="bg-[var(--primary-red)] hover:bg-[var(--hover-accent)] text-white px-4 py-2 rounded-lg transition-colors"
        >
          + Nuovo Dipendente
        </button>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 rounded-lg text-sm ${
              filter === 'all'
                ? 'bg-[var(--primary-red)] text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Tutti
          </button>
          <button
            onClick={() => setFilter('active')}
            className={`px-3 py-1 rounded-lg text-sm ${
              filter === 'active'
                ? 'bg-[var(--primary-red)] text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Attivi
          </button>
          <button
            onClick={() => setFilter('inactive')}
            className={`px-3 py-1 rounded-lg text-sm ${
              filter === 'inactive'
                ? 'bg-[var(--primary-red)] text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Inattivi
          </button>
        </div>

        <div className="flex-1">
          <input
            type="text"
            placeholder="Cerca dipendenti..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-red)] focus:border-transparent"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Employee List */}
      {filteredEmployees.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {searchTerm ? 'Nessun dipendente trovato con i criteri di ricerca.' : 'Nessun dipendente trovato.'}
        </div>
      ) : (
        <div className="grid gap-4">
          {filteredEmployees.map((employee) => (
            <div
              key={employee.id}
              className={`bg-white rounded-lg shadow-sm border p-4 ${
                !employee.is_active ? 'opacity-60' : ''
              }`}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-semibold text-[var(--primary-text)]">
                      {employee.name}
                    </h3>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        employee.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {employee.is_active ? 'Attivo' : 'Inattivo'}
                    </span>
                  </div>
                  
                  <div className="mt-2 space-y-1 text-sm text-[var(--secondary-text)]">
                    {employee.email && (
                      <p><strong>Email:</strong> {employee.email}</p>
                    )}
                    {employee.role && (
                      <p><strong>Ruolo:</strong> {employee.role}</p>
                    )}
                    {employee.department && (
                      <p><strong>Dipartimento:</strong> {employee.department}</p>
                    )}
                    {employee.phone && (
                      <p><strong>Telefono:</strong> {employee.phone}</p>
                    )}
                    {employee.specializations && employee.specializations.length > 0 && (
                      <p><strong>Specializzazioni:</strong> {employee.specializations.join(', ')}</p>
                    )}
                  </div>
                </div>

                <div className="flex gap-2 ml-4">
                  <button
                    onClick={() => onEditEmployee(employee)}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors"
                  >
                    Modifica
                  </button>
                  <button
                    onClick={() => handleDelete(employee)}
                    disabled={employee.name === 'Qualsiasi'}
                    className={`px-3 py-1 rounded text-sm transition-colors ${
                      employee.name === 'Qualsiasi'
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-red-500 hover:bg-red-600 text-white'
                    }`}
                  >
                    Elimina
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
