// Cookie Consent Management Utilities

const CONSENT_STORAGE_KEY = 'caf_cookie_consent';
const CONSENT_VERSION = '1.0';

/**
 * Default consent preferences
 */
const DEFAULT_CONSENT = {
  necessary: true, // Always true, cannot be disabled
  analytics: false,
  marketing: false,
  preferences: false,
  version: CONSENT_VERSION,
  timestamp: null,
};

/**
 * Get current cookie consent preferences
 * @returns {Object|null} Consent preferences or null if not set
 */
export const getCookieConsent = () => {
  if (typeof window === 'undefined') return null;
  
  try {
    const stored = localStorage.getItem(CONSENT_STORAGE_KEY);
    if (!stored) return null;
    
    const consent = JSON.parse(stored);
    
    // Check if consent version is current
    if (consent.version !== CONSENT_VERSION) {
      return null; // Force re-consent for new version
    }
    
    return consent;
  } catch (error) {
    console.error('Error reading cookie consent:', error);
    return null;
  }
};

/**
 * Save cookie consent preferences
 * @param {Object} consent - Consent preferences
 */
export const saveCookieConsent = (consent) => {
  if (typeof window === 'undefined') return;
  
  const consentData = {
    ...DEFAULT_CONSENT,
    ...consent,
    necessary: true, // Always true
    version: CONSENT_VERSION,
    timestamp: new Date().toISOString(),
  };
  
  try {
    localStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(consentData));
    
    // Update Google Analytics consent
    if (typeof window.updateGAConsent === 'function') {
      window.updateGAConsent(consentData);
    }
    
    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('consentUpdated', {
      detail: consentData
    }));
    
    return consentData;
  } catch (error) {
    console.error('Error saving cookie consent:', error);
    return null;
  }
};

/**
 * Check if user has given consent for a specific category
 * @param {string} category - Consent category (analytics, marketing, preferences)
 * @returns {boolean} Whether consent is given
 */
export const hasConsent = (category) => {
  const consent = getCookieConsent();
  if (!consent) return false;
  
  return consent[category] === true;
};

/**
 * Check if consent banner should be shown
 * @returns {boolean} Whether to show the consent banner
 */
export const shouldShowConsentBanner = () => {
  return getCookieConsent() === null;
};

/**
 * Accept all cookies
 * @returns {Object} Updated consent preferences
 */
export const acceptAllCookies = () => {
  return saveCookieConsent({
    necessary: true,
    analytics: true,
    marketing: true,
    preferences: true,
  });
};

/**
 * Reject all non-necessary cookies
 * @returns {Object} Updated consent preferences
 */
export const rejectAllCookies = () => {
  return saveCookieConsent({
    necessary: true,
    analytics: false,
    marketing: false,
    preferences: false,
  });
};

/**
 * Clear all consent data (for testing or reset)
 */
export const clearConsent = () => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(CONSENT_STORAGE_KEY);
    
    // Reset Google Analytics consent to default (denied)
    if (typeof window.gtag === 'function') {
      window.gtag('consent', 'update', {
        analytics_storage: 'denied',
        ad_storage: 'denied',
        ad_user_data: 'denied',
        ad_personalization: 'denied',
      });
    }
  } catch (error) {
    console.error('Error clearing consent:', error);
  }
};

/**
 * Get consent categories with descriptions
 * @returns {Array} Array of consent categories
 */
export const getConsentCategories = () => {
  return [
    {
      id: 'necessary',
      name: 'Cookie Necessari',
      description: 'Questi cookie sono essenziali per il funzionamento del sito web e non possono essere disabilitati.',
      required: true,
    },
    {
      id: 'analytics',
      name: 'Cookie Analitici',
      description: 'Questi cookie ci aiutano a capire come i visitatori interagiscono con il sito web raccogliendo e riportando informazioni in modo anonimo.',
      required: false,
    },
    {
      id: 'marketing',
      name: 'Cookie di Marketing',
      description: 'Questi cookie vengono utilizzati per tracciare i visitatori sui siti web per mostrare annunci pertinenti e coinvolgenti.',
      required: false,
    },
    {
      id: 'preferences',
      name: 'Cookie delle Preferenze',
      description: 'Questi cookie permettono al sito web di ricordare le scelte che fai per fornirti funzionalità migliorate e personalizzate.',
      required: false,
    },
  ];
};
