// Test per verificare il login admin
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carica il file .env.local
dotenv.config({ path: join(__dirname, '.env.local') });

// Importa le funzioni
import { validateAdminCredentials } from './lib/supabaseAuthUtils.js';

const testLogin = async () => {
  console.log('=== TEST LOGIN ADMIN ===');
  
  const username = process.env.ADMIN_USERNAME;
  const password = process.env.ADMIN_PASSWORD;
  
  console.log('Tentativo di login con:');
  console.log('Username:', username);
  console.log('Password:', password ? '[HIDDEN]' : 'NOT SET');
  
  try {
    const user = await validateAdminCredentials(username, password);
    
    if (user) {
      console.log('\n✅ LOGIN RIUSCITO!');
      console.log('User ID:', user.id);
      console.log('Username:', user.username);
      console.log('Email:', user.email);
      console.log('Role:', user.role);
    } else {
      console.log('\n❌ LOGIN FALLITO!');
      console.log('Credenziali non valide');
    }
  } catch (error) {
    console.log('\n❌ ERRORE DURANTE IL LOGIN:');
    console.log(error.message);
    console.log(error.stack);
  }
};

testLogin();
